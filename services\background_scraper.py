"""
Background Scraper Service
This service handles background scraping of Telegram channels and processing of messages.
"""
import asyncio
import time
import traceback
import datetime
import pytz
import random
from typing import Dict, Any, List, Optional
from telethon import TelegramClient
from telethon.errors import FloodWaitError, SessionPasswordNeededError
from telethon.tl.types import Channel, Chat, User
from sqlalchemy.ext.asyncio import AsyncSession
from utils.logger import Logger
from services.database import DatabaseService
from models.register import Register
from models.tg_entity import TgEntity
from models.message import Message
from models.tg_content import TgContent
from models.register_tg_entity import RegisterTgEntity
from helpers.register_helpers import get_register_tg_entities, get_register_by_project_id, get_active_registers_for_entity
from helpers.telegram_helpers import create_telegram_client, get_channel_messages, get_historical_channel_messages
from helpers.in_memory_state_manager import InMemoryStateManager
from helpers.message_helpers import mark_crawling_as_completed
from helpers.bot_state_helpers import update_last_post_time, update_last_reply_time
from helpers.tg_entity_helpers import update_tg_entity_details
bg_scraper_logger = Logger("background_scraper")

class BackgroundScraperService:
    """Service for background scraping of Telegram channels"""
    def __init__(self, db_service: DatabaseService, config: Dict[str, Any]):
        self.db_service = db_service
        self.config = config
        self.running = False
        self.scraping_tasks = {}
        self.state_manager = InMemoryStateManager()  # Use in-memory state manager
        # Get historical scraping days from config, default to 90 days (3 months)
        self.historical_scraping_days = config.get('application', {}).get('historical_scraping_days', 90)
        # Track entities being scraped to avoid concurrent scraping
        self.entities_being_scraped = set()

    async def start(self):
        """Start the background scraper service"""
        # Create a single section for the entire method execution
        with bg_scraper_logger.begin_section("Service Lifecycle") as section:
            section.log_step("Starting background scraper service")
        self.running = True
        # Start the main scraping loop
        self._scraping_loop_task = asyncio.create_task(self._scraping_loop())
        # Minimal delay to allow task scheduling without blocking
        await asyncio.sleep(0.01)

    async def stop(self):
        """Stop the background scraper service"""
        # Create a single section for the entire method execution
        with bg_scraper_logger.begin_section("Service Lifecycle") as section:
            section.log_step("Stopping background scraper service")
        self.running = False
        # Cancel all running scraping tasks
        for task in self.scraping_tasks.values():
            task.cancel()
        # Cancel the main scraping loop task
        if hasattr(self, '_scraping_loop_task') and self._scraping_loop_task:
            self._scraping_loop_task.cancel()
        # Wait for tasks to complete
        tasks_to_wait = list(self.scraping_tasks.values())
        if hasattr(self, '_scraping_loop_task') and self._scraping_loop_task:
            tasks_to_wait.append(self._scraping_loop_task)
        if tasks_to_wait:
            await asyncio.gather(*tasks_to_wait, return_exceptions=True)

    async def add_user(self, phone_number: str):
        """Add a user to be scraped"""
        with bg_scraper_logger.begin_section("User Management") as section:
            section.log_step(f"Adding user to scraping queue: {phone_number}")
        # Get user from database
        session = await self.db_service.get_session()
        try:
            # Use select statement with execute instead of query
            from sqlalchemy import select
            stmt = select(Register).where(Register.phone_number == phone_number)
            result = await session.execute(stmt)
            register = result.scalars().first()
            if register and register.active:
                # Create scraping task for this user
                task = asyncio.create_task(self._scrape_user(register))
                self.scraping_tasks[phone_number] = task
                # Add exception handler using a standalone function to avoid closure issues
                def callback(task, service_instance=self, pn=phone_number):
                    service_instance._handle_scraping_task_completion(task, pn)
                task.add_done_callback(callback)
            else:
                with bg_scraper_logger.begin_section("User Management") as section:
                    section.log_step("User not found or not active")
        finally:
            await session.close()

    def _handle_scraping_task_completion(self, task: asyncio.Task, phone_number: str):
        """Handle completion of a scraping task"""
        try:
            # This will raise any exceptions that occurred, including CancelledError
            task.result()
        except asyncio.CancelledError:
            with bg_scraper_logger.begin_section("Task Completion") as section:
                section.log_step(f"Scraping task was cancelled for phone number: {phone_number}")
        except Exception as e:
            with bg_scraper_logger.begin_section("Task Completion") as section:
                section.log_step(f"Scraping task failed for phone number: {phone_number}")
                section.log_step(f"Error details - Error: {str(e)}")
        finally:
            # Remove task from tracking
            if phone_number in self.scraping_tasks:
                del self.scraping_tasks[phone_number]

    async def _scraping_loop(self):
        """Main scraping loop - now scrapes entities directly instead of per user"""
        while self.running:
            try:
                # Create a single section for the entire method execution
                with bg_scraper_logger.begin_section("Backend Crawling Process") as section:
                    section.log_step("Starting scraping cycle")
                    # Get all active tg_entities with their associated active registers
                    session = await self.db_service.get_session()
                    try:
                        from sqlalchemy import select
                        # Get all register-tg_entity relationships for active registers
                        stmt = select(RegisterTgEntity).join(
                            Register, Register.project_id == RegisterTgEntity.project_id
                        ).where(Register.active == 1)
                        result = await session.execute(stmt)
                        relationships = result.scalars().all()
                        section.log_step(f"Active entities found: {len(relationships)}")
                        # Group relationships by tg_entity_id
                        entity_relationships = {}
                        for relationship in relationships:
                            if relationship.tg_entity_id not in entity_relationships:
                                entity_relationships[relationship.tg_entity_id] = []
                            entity_relationships[relationship.tg_entity_id].append(relationship)
                        section.log_step(f"Processing entities: {len(entity_relationships)}")
                        # Scrape each entity using a randomly selected API
                        for entity_id, relationships in entity_relationships.items():
                            if not self.running:
                                section.log_step("Scraper stopped, interrupting scraping cycle")
                                break
                            # Check if entity is already being scraped
                            if entity_id in self.entities_being_scraped:
                                section.log_step(f"Entity already being scraped, skipping: {entity_id}")
                                continue
                            # Mark entity as being scraped
                            self.entities_being_scraped.add(entity_id)
                            try:
                                # Get the entity details
                                entity_stmt = select(TgEntity).where(TgEntity.id == entity_id)
                                entity_result = await session.execute(entity_stmt)
                                entity = entity_result.scalars().first()
                                if entity:
                                    section.log_step(f"Scraping entity: {entity.name}")
                                    section.log_step(f"Entity details - Entity ID: {entity.id}, Available registers: {len(relationships)}")
                                    await self._scrape_entity_with_random_api(entity, relationships)
                                else:
                                    section.log_step(f"Entity not found: {entity_id}")
                            finally:
                                self.entities_being_scraped.discard(entity_id)
                    finally:
                        await session.close()
                    section.log_step("Scraping cycle completed, waiting for next cycle")
                # Wait before next iteration - increased to 5 minutes to reduce database load
                await asyncio.sleep(300)  # Check every 5 minutes instead of every minute
            except Exception as e:
                # Handle case where section might not be defined yet
                with bg_scraper_logger.begin_section("Backend Crawling Process") as section:
                    section.log_step("Error in scraping loop")
                    section.log_step(f"Error details - Error: {str(e)}")
                await asyncio.sleep(60)  # Wait before retrying

    async def _get_random_register_from_relationships(self, relationships: List[RegisterTgEntity]) -> Optional[Register]:
        """
        Get a random active register from a list of register-tg_entity relationships.
        Args:
            relationships (List[RegisterTgEntity]): List of register-tg_entity relationships
        Returns:
            Register: A randomly selected active register, or None if none found
        """
        if not relationships:
            return None
        # Get all project_ids from relationships
        project_ids = [rel.project_id for rel in relationships]
        session = await self.db_service.get_session()
        try:
            # Get all active registers for these project_ids
            from sqlalchemy import select
            stmt = select(Register).where(
                Register.project_id.in_(project_ids),
                Register.active == 1
            )
            result = await session.execute(stmt)
            registers = result.scalars().all()
            if not registers:
                with bg_scraper_logger.begin_section("Entity Processing") as section:
                    section.log_step("No active registers found for project_ids")
                    section.log_step(f"Project IDs: {project_ids}")
                return None
            # Randomly select one register
            selected_register = random.choice(registers)
            with bg_scraper_logger.begin_section("Entity Processing") as section:
                section.log_step(f"Randomly selected register for scraping: {selected_register.phone_number}")
            return selected_register
        except Exception as e:
            with bg_scraper_logger.begin_section("Entity Processing") as section:
                section.log_step("Error getting random register from relationships")
                section.log_step(f"Error details - Error: {str(e)}")
            return None
        finally:
            await session.close()

    async def _scrape_entity_with_random_api(self, entity: TgEntity, relationships: List[RegisterTgEntity]):
        """
        Scrape a specific entity using a randomly selected API ID from active registers.
        Args:
            entity (TgEntity): The entity to scrape
            relationships (List[RegisterTgEntity]): List of register-tg_entity relationships for this entity
        """
        try:
            # Wrap the entire method body in a single 'with' block
            with bg_scraper_logger.begin_section("Entity Processing") as section:
                # Get a random active register for this entity
                register = await self._get_random_register_from_relationships(relationships)
                if not register:
                    section.log_step(f"No active register found for entity: {entity.name}")
                    return

                # Create Telegram client with the selected register's API credentials
                client = None
                try:
                    client = await create_telegram_client(
                        register.phone_number,
                        register.api_id,
                        register.api_hash,
                        register.tg_bot_token
                    )
                    await client.start()
                    section.log_step(f"Connected to Telegram: {register.phone_number}")
                    section.log_step(f"Entity details - Entity name: {entity.name}")
                    # Add a small delay after connecting to ensure stability
                    await asyncio.sleep(1)

                    # Get the last processed message ID for this channel
                    channel_key = f"{register.phone_number}_{entity.name}"
                    last_processed_id = self.state_manager.get_last_processed_id(channel_key)

                    # Check if there's existing tg_content for this entity
                    session = await self.db_service.get_session()
                    try:
                        from sqlalchemy import select
                        from models.tg_content import TgContent
                        stmt = select(TgContent).where(TgContent.entities_id == entity.id).limit(1)
                        result = await session.execute(stmt)
                        existing_content = result.scalars().first()
                        has_existing_content = existing_content is not None
                    finally:
                        await session.close()

                    # Initialize messages variable
                    messages = []

                    # If no existing content, perform historical scraping
                    if not has_existing_content and entity.tg_id:
                        # Calculate the date from which to scrape (based on config)
                        days_back = self.historical_scraping_days
                        section.log_step("Performing historical scraping for entity with no existing content")
                        section.log_step(f"Scraping parameters - Days back: {days_back}, Entity name: {entity.name}")
                        # Perform historical scraping
                        from helpers.telegram_helpers import get_historical_channel_messages
                        messages = await get_historical_channel_messages(
                            client,
                            int(entity.tg_id),  # Convert to int
                            entity.access_hash,
                            days_back
                        )
                    elif entity.tg_id:
                        # Regular incremental scraping
                        section.log_step(f"Performing incremental scraping for: {channel_key}")
                        section.log_step(f"Last processed ID: {last_processed_id}")
                        # Get messages from channel
                        from helpers.telegram_helpers import get_channel_messages
                        messages = await get_channel_messages(
                            client,
                            int(entity.tg_id),  # Convert to int
                            entity.access_hash,
                            last_processed_id
                        )

                    if not messages:
                        section.log_step(f"No new messages in channel for entity: {entity.name}")
                        return

                    section.log_step(f"Retrieved messages from channel: {entity.tg_id}")
                    section.log_step(f"Message count: {len(messages)}")

                    # Store raw messages without LLM processing
                    raw_messages = []
                    max_message_id = last_processed_id  # Initialize to last processed ID

                    # Process messages and extract relevant information
                    for message in messages:
                        try:
                            # Convert Telethon Message object to dictionary safely
                            raw_message = {
                                'message_id': getattr(message, 'id', None),
                                'message': getattr(message, 'message', ''),
                                'date': getattr(message, 'date', None),
                                'sender_id': str(getattr(message, 'sender_id', '')) if getattr(message, 'sender_id', '') is not None else '',
                                'reply_to_msg_id': getattr(message, 'reply_to_msg_id', None),
                            }
                            # Only save messages with content
                            if raw_message['message'].strip():
                                raw_messages.append(raw_message)
                            # Track the maximum message ID
                            message_id = getattr(message, 'id', 0)
                            if message_id > max_message_id:
                                max_message_id = message_id
                        except Exception as e:
                            # Get message ID safely
                            message_id = getattr(message, 'id', 'unknown')
                            section.log_step(f"Error processing message: {message_id}")
                            section.log_step(f"Error details - Error: {str(e)}")
                            # Still save the message even if processing fails, but only if it has content
                            raw_message = {
                                'message_id': getattr(message, 'id', None),
                                'message': getattr(message, 'message', ''),
                                'date': getattr(message, 'date', None),
                                'sender_id': str(getattr(message, 'sender_id', '')) if getattr(message, 'sender_id', '') is not None else '',
                                'reply_to_msg_id': getattr(message, 'reply_to_msg_id', None),
                            }
                            if raw_message['message'].strip():
                                raw_messages.append(raw_message)

                    # Update last processed ID after processing all messages
                    if max_message_id > last_processed_id:
                        self.state_manager.update_last_processed_id(channel_key, max_message_id)
                        last_processed_id = max_message_id

                    # Save messages to database
                    session = await self.db_service.get_session()
                    try:
                        await self._save_messages_batch(session, raw_messages, register.project_id, entity.id)
                        await session.commit()
                        # The _save_messages_batch method now handles the logging correctly
                    except Exception as e:
                        await session.rollback()
                        section.log_step(f"Error saving messages to database for: {entity.name}")
                        section.log_step(f"Error details - Error: {str(e)}")
                        raise
                    finally:
                        await session.close()

                except Exception as e:
                    # Handle case where section might not be defined yet
                    # Since we are in a 'with' block, 'section' is guaranteed to exist here
                    section.log_step(f"Error scraping entity with register: {entity.name}")
                    section.log_step(f"Register details - Phone number: {register.phone_number if 'register' in locals() else 'unknown'}, Error: {str(e)}")
                    raise
        finally:
            # Clean up client if it was created
            if 'client' in locals() and client:
                try:
                    await client.disconnect()
                except:
                    pass

    async def _scrape_user(self, register: Register):
        """Scrape messages for a specific user - kept for backward compatibility"""
        with bg_scraper_logger.begin_section("User Scraping") as section:
            section.log_step(f"Starting scraping for user: {register.phone_number}")
            section.log_step(f"Project ID: {register.project_id}")

        client = None
        try:
            # Create Telegram client
            client = await create_telegram_client(
                register.phone_number,
                register.api_id,
                register.api_hash,
                register.tg_bot_token
            )
            # Connect to Telegram with retry logic for database lock issues
            retry_count = 0
            max_retries = 3
            while retry_count < max_retries:
                try:
                    await client.start()
                    with bg_scraper_logger.begin_section("Telegram Client") as section:
                        section.log_step(f"Connected to Telegram for user: {register.phone_number}")
                    break
                except Exception as e:
                    error_str = str(e).lower()
                    if "database is locked" in error_str or "process cannot access the file" in error_str:
                        retry_count += 1
                        with bg_scraper_logger.begin_section("Telegram Client") as section:
                            section.log_step(f"Database locked, retrying for user: {register.phone_number}")
                            section.log_step(f"Retry details - Retry count: {retry_count}, Max retries: {max_retries}")
                        if retry_count < max_retries:
                            # Wait 5 seconds before retrying
                            await asyncio.sleep(5)
                            # Try to handle the database lock
                            from helpers.telegram_helpers import handle_database_lock
                            if not handle_database_lock(register.phone_number):
                                with bg_scraper_logger.begin_section("Telegram Client") as section:
                                    section.log_step(f"Failed to handle database lock for user: {register.phone_number}")
                        else:
                            # If we've exhausted retries, try to clean up session files
                            from helpers.telegram_helpers import cleanup_session_files
                            if not cleanup_session_files(register.phone_number):
                                with bg_scraper_logger.begin_section("Telegram Client") as section:
                                    section.log_step(f"Failed to clean up session files for user: {register.phone_number}")
                            raise  # Re-raise the exception
                    else:
                        raise  # Re-raise if it's not a database lock error

            # Add a small delay after connecting to ensure stability
            await asyncio.sleep(1)

            # Get channels to scrape
            session = await self.db_service.get_session()
            try:
                tg_entities = await get_register_tg_entities(session, register.project_id)
                with bg_scraper_logger.begin_section("User Scraping") as section:
                    section.log_step(f"Found entities to scrape for user: {register.phone_number}")
                    section.log_step(f"Entity count: {len(tg_entities)}")
            finally:
                await session.close()

            # Scrape each channel
            for i, entity in enumerate(tg_entities, 1):
                if not self.running:
                    with bg_scraper_logger.begin_section("User Scraping") as section:
                        section.log_step(f"Scraper stopped, interrupting scraping for user: {register.phone_number}")
                    break
                try:
                    with bg_scraper_logger.begin_section("User Scraping") as section:
                        section.log_step(f"Scraping channel for user: {register.phone_number}")
                        section.log_step(f"Channel details - Channel index: {i}, Total channels: {len(tg_entities)}, Entity name: {entity.name}")
                    await self._scrape_channel(client, register, entity)
                    with bg_scraper_logger.begin_section("User Scraping") as section:
                        section.log_step(f"Completed scraping channel: {entity.name}")
                        section.log_step(f"Progress - Channel index: {i}, Total channels: {len(tg_entities)}")
                except Exception as e:
                    with bg_scraper_logger.begin_section("User Scraping") as section:
                        section.log_step(f"Error scraping channel: {entity.name}")
                        section.log_step(f"Error details - Phone number: {register.phone_number}, Error: {str(e)}")
        except Exception as e:
            with bg_scraper_logger.begin_section("User Scraping") as section:
                section.log_step(f"Error scraping for user: {register.phone_number}")
                section.log_step(f"Error details - Error: {str(e)}")
            # Clean up client if it was created
            if client:
                try:
                    await client.disconnect()
                except:
                    pass
        finally:
            # Mark crawling as completed for this project
            try:
                session = await self.db_service.get_session()
                try:
                    await mark_crawling_as_completed(session, register.project_id)
                    # Update bot state with last scraping time
                    current_time = int(time.time())
                    await update_last_post_time(session, register.project_id, current_time)
                    with bg_scraper_logger.begin_section("User Scraping") as section:
                        section.log_step(f"Marked crawling as completed for project: {register.project_id}")
                finally:
                    await session.close()
            except Exception as e:
                with bg_scraper_logger.begin_section("User Scraping") as section:
                    section.log_step(f"Error marking crawling as completed for project: {register.project_id}")
                    section.log_step(f"Error details - Error: {str(e)}")
            with bg_scraper_logger.begin_section("User Scraping") as section:
                section.log_step(f"Finished scraping for user: {register.phone_number}")

    async def _scrape_channel(self, client: TelegramClient, register: Register, entity: TgEntity):
        """Scrape messages from a specific channel using a randomly selected API ID"""
        with bg_scraper_logger.begin_section("Channel Scraping") as section:
            section.log_step(f"Scraping channel: {entity.name}")
            section.log_step(f"Channel details - Channel ID: {entity.tg_id}, Phone number: {register.phone_number}")

        try:
            # Check if there's existing tg_content for this entity
            session = await self.db_service.get_session()
            try:
                from sqlalchemy import select
                from models.tg_content import TgContent
                stmt = select(TgContent).where(TgContent.entities_id == entity.id).limit(1)
                result = await session.execute(stmt)
                existing_content = result.scalars().first()
                has_existing_content = existing_content is not None
            finally:
                await session.close()

            # Get the last processed message ID for this channel
            channel_key = f"{register.phone_number}_{entity.name}"
            last_processed_id = self.state_manager.get_last_processed_id(channel_key)

            # Initialize messages variable
            messages = []

            # Check if we need to do historical scraping
            start_time = self.state_manager.get_start_time(channel_key)
            if start_time == 0 and not has_existing_content:
                # This is the first time scraping this channel and no existing content, do historical scraping
                # Set start time based on configuration, default to 90 days (3 months) ago
                self.state_manager.set_historical_start_time(channel_key, self.historical_scraping_days)
                start_time = self.state_manager.get_start_time(channel_key)
                with bg_scraper_logger.begin_section("Historical Scraping") as section:
                    section.log_step(f"Performing historical scraping for: {channel_key}")
                    section.log_step(f"Configuration - Days back: {self.historical_scraping_days}")
                # Convert timestamp to datetime with UTC timezone for consistency
                start_date = datetime.datetime.fromtimestamp(start_time, tz=pytz.UTC)
                # Get historical messages using the new function
                from helpers.telegram_helpers import get_historical_channel_messages
                messages = await get_historical_channel_messages(
                    client,
                    int(entity.tg_id),  # Convert to int
                    entity.access_hash,
                    start_date
                )
                with bg_scraper_logger.begin_section("Historical Scraping") as section:
                    section.log_step(f"Retrieved historical messages for: {entity.name}")
                    section.log_step(f"Message count: {len(messages)}")
            elif start_time == 0 and has_existing_content:
                # No start time but has existing content, skip historical scraping and do incremental
                with bg_scraper_logger.begin_section("Historical Scraping") as section:
                    section.log_step(f"Skipping historical scraping - existing content found for: {channel_key}")
                # Set to current time to prevent future historical scraping
                self.state_manager.set_historical_start_time(channel_key, 0)  # Set to 0 days back
                # Get incremental messages from last processed ID
                from helpers.telegram_helpers import get_channel_messages
                messages = await get_channel_messages(
                    client,
                    int(entity.tg_id),  # Convert to int
                    entity.access_hash,
                    last_processed_id
                )
            else:
                # Regular incremental scraping
                with bg_scraper_logger.begin_section("Incremental Scraping") as section:
                    section.log_step(f"Performing incremental scraping for: {channel_key}")
                    section.log_step(f"Last processed ID: {last_processed_id}")
                # Get messages from channel
                from helpers.telegram_helpers import get_channel_messages
                messages = await get_channel_messages(
                    client,
                    int(entity.tg_id),  # Convert to int
                    entity.access_hash,
                    last_processed_id
                )

            if not messages:
                with bg_scraper_logger.begin_section("Message Processing") as section:
                    section.log_step(f"No new messages in channel: {entity.name}")
                return

            with bg_scraper_logger.begin_section("Message Processing") as section:
                section.log_step(f"Retrieved messages from channel: {entity.tg_id}")
                section.log_step(f"Message count: {len(messages)}")

            # Store raw messages without LLM processing
            raw_messages = []
            max_message_id = last_processed_id  # Track the maximum message ID processed
            for message in messages:
                try:
                    # Convert Telethon Message object to dictionary
                    # Telethon Message objects don't have a .dict() method, so we need to extract attributes manually
                    raw_message = {
                        'message_id': getattr(message, 'id', None),
                        'message': getattr(message, 'message', ''),
                        'date': getattr(message, 'date', None),
                        'sender_id': str(getattr(message, 'sender_id', '')) if getattr(message, 'sender_id', '') is not None else '',
                        'reply_to_msg_id': getattr(message, 'reply_to_msg_id', None),
                        # Add other relevant attributes as needed
                    }
                    # Only save messages with content
                    if raw_message['message'].strip():
                        raw_messages.append(raw_message)
                    # Track the maximum message ID
                    message_id = getattr(message, 'id', 0)
                    if message_id > max_message_id:
                        max_message_id = message_id
                except Exception as e:
                    # Get message ID safely
                    message_id = getattr(message, 'id', 'unknown')
                    with bg_scraper_logger.begin_section("Message Processing") as section:
                        section.log_step(f"Error processing message: {message_id}")
                        section.log_step(f"Error details - Error: {str(e)}")
                    # Still save the message even if processing fails, but only if it has content
                    # Convert Telethon Message object to dictionary safely
                    raw_message = {
                        'message_id': getattr(message, 'id', None),
                        'message': getattr(message, 'message', ''),
                        'date': getattr(message, 'date', None),
                        'sender_id': str(getattr(message, 'sender_id', '')) if getattr(message, 'sender_id', '') is not None else '',
                        'reply_to_msg_id': getattr(message, 'reply_to_msg_id', None),
                    }
                    if raw_message['message'].strip():
                        raw_messages.append(raw_message)

            # Update last processed ID after processing all messages
            if max_message_id > last_processed_id:
                self.state_manager.update_last_processed_id(channel_key, max_message_id)
                last_processed_id = max_message_id

            # Save messages to database
            session = await self.db_service.get_session()
            try:
                await self._save_messages_batch(session, raw_messages, register.project_id, entity.id)
                await session.commit()
                # The _save_messages_batch method now handles the logging correctly
            except Exception as e:
                await session.rollback()
                with bg_scraper_logger.begin_section("Database Operations") as section:
                    section.log_step(f"Error saving messages to database for: {entity.name}")
                    section.log_step(f"Error details - Error: {str(e)}")
                raise
            finally:
                await session.close()
        except Exception as e:
            with bg_scraper_logger.begin_section("Channel Scraping") as section:
                section.log_step(f"Error scraping channel: {entity.name}")
                section.log_step(f"Error details - Error: {str(e)}")
            raise

    async def _save_messages_batch(self, session: AsyncSession, messages: List[Dict], project_id: int, entity_id: int):
        """
        Save a batch of messages to the database with duplicate checking.
        Args:
            session: Database session
            messages: List of message dictionaries to save
            project_id: Project ID for these messages
            entity_id: Entity ID for these messages
        """
        try:
            # Create a single section for the entire method execution
            with bg_scraper_logger.begin_section("Database Operations") as section:
                if not messages:
                    section.log_step("No messages to save")
                    section.log_step(f"Project details - Project ID: {project_id}, Entity ID: {entity_id}")
                    return

                # Log the number of messages to be processed
                section.log_step("Processing batch of messages for saving")
                section.log_step(f"Batch details - Total messages: {len(messages)}, Project ID: {project_id}, Entity ID: {entity_id}")

                # Prepare tg_content objects
                tg_content_objects = []
                duplicate_count = 0
                for raw_message in messages:
                    try:
                        # Extract message data
                        message_id = raw_message.get('message_id')
                        content = raw_message.get('message', '')
                        date = raw_message.get('date')
                        user_id = raw_message.get('sender_id')
                        reply_to_msg_id = raw_message.get('reply_to_msg_id')

                        # Skip empty messages
                        if not content or not content.strip():
                            continue

                        # Convert date to timestamp if it exists
                        created_at = int(date.timestamp()) if date else int(time.time())

                        # Check for duplicates in the current batch
                        is_duplicate_in_batch = any(
                            existing.content == content and existing.entities_id == entity_id
                            for existing in tg_content_objects
                        )
                        if is_duplicate_in_batch:
                            duplicate_count += 1
                            continue

                        # Check for duplicates in the database
                        from sqlalchemy import select
                        from models.tg_content import TgContent
                        stmt = select(TgContent).where(
                            TgContent.content == content,
                            TgContent.entities_id == entity_id
                        )
                        result = await session.execute(stmt)
                        existing_content = result.scalars().first()
                        if existing_content:
                            duplicate_count += 1
                            continue

                        # Create new tg_content object
                        tg_content = TgContent(
                            entities_id=entity_id,
                            message_id=message_id,
                            user_id=user_id,
                            content=content,
                            message_date=date,
                            reply_to=reply_to_msg_id,
                            created_at=created_at
                        )
                        tg_content_objects.append(tg_content)
                    except Exception as e:
                        section.log_step(f"Error preparing message for saving - Error: {str(e)}, Project ID: {project_id}, Entity ID: {entity_id}")
                        # Continue with other messages even if one fails
                        continue

                # Bulk save all tg_content objects
                if tg_content_objects:
                    try:
                        session.add_all(tg_content_objects)
                        await session.flush()  # Flush to get IDs but don't commit yet
                        section.log_step("Saved new messages to database")
                        section.log_step(f"Saved messages - Count: {len(tg_content_objects)}, Project ID: {project_id}, Entity ID: {entity_id}")
                        # Log structured information for debugging
                        section.log_step("Messages processing completed")
                        section.log_step(f"Processing summary - Total: {len(messages)}, New: {len(tg_content_objects)}, Duplicates: {duplicate_count}, Entity ID: {entity_id}, Project ID: {project_id}")
                    except Exception as e:
                        section.log_step(f"Error saving messages to database - Error: {str(e)}, Project ID: {project_id}, Entity ID: {entity_id}")
                elif duplicate_count > 0:
                    # Log that we processed messages but they were all duplicates
                    section.log_step("Processed messages, all were duplicates - no new messages saved")
                    section.log_step(f"Processing summary - Total messages: {len(messages)}, Duplicate count: {duplicate_count}, Project ID: {project_id}, Entity ID: {entity_id}")
                    # Log structured information for debugging
                    section.log_step("Messages processing completed")
                    section.log_step(f"Processing summary - Total: {len(messages)}, New: 0, Duplicates: {duplicate_count}, Entity ID: {entity_id}, Project ID: {project_id}")

        except Exception as e:
            # Re-raise if needed, or handle at higher level
            raise
